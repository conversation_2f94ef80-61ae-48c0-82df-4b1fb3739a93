# 结算单时间参数标准化处理测试

## 测试目标
验证前后端时间参数处理的一致性，确保时间范围查询的准确性和边界情况处理。

## 测试场景

### 1. 前端时间参数转换测试

#### 测试用例1：正常日期范围
- **输入**: dateRange = ["2024-01-15", "2024-01-20"]
- **期望输出**: 
  - settlementStartTime = 1705248000 (2024-01-15 00:00:00 UTC)
  - settlementEndTime = 1705679999 (2024-01-20 23:59:59 UTC)

#### 测试用例2：单日期范围
- **输入**: dateRange = ["2024-01-15", "2024-01-15"]
- **期望输出**:
  - settlementStartTime = 1705248000 (2024-01-15 00:00:00 UTC)
  - settlementEndTime = 1705334399 (2024-01-15 23:59:59 UTC)

#### 测试用例3：跨月日期范围
- **输入**: dateRange = ["2024-01-31", "2024-02-01"]
- **期望输出**:
  - settlementStartTime = 1706659200 (2024-01-31 00:00:00 UTC)
  - settlementEndTime = 1706831999 (2024-02-01 23:59:59 UTC)

### 2. 后端时间参数验证测试

#### 测试用例1：有效时间戳
- **输入**: settlementStartTime = 1705248000, settlementEndTime = 1705679999
- **期望结果**: 验证通过，转换为日期字符串存储

#### 测试用例2：无效时间戳（负数）
- **输入**: settlementStartTime = -1, settlementEndTime = 1705679999
- **期望结果**: 验证失败，返回错误信息

#### 测试用例3：无效时间戳（超出范围）
- **输入**: settlementStartTime = 5000000000, settlementEndTime = 5000000001
- **期望结果**: 验证失败，返回错误信息

#### 测试用例4：时间范围错误
- **输入**: settlementStartTime = 1705679999, settlementEndTime = 1705248000
- **期望结果**: 验证失败，返回"开始时间必须小于结束时间"

### 3. 兼容性测试

#### 测试用例1：旧格式日期字符串
- **输入**: startDate = "2024-01-15", endDate = "2024-01-20"
- **期望结果**: 正常处理，向后兼容

#### 测试用例2：混合参数格式
- **输入**: settlementStartTime = 1705248000, endDate = "2024-01-20"
- **期望结果**: 验证失败，要求参数格式一致

### 4. 边界情况测试

#### 测试用例1：时区处理
- **输入**: 不同时区的日期选择
- **期望结果**: 统一转换为UTC时间戳

#### 测试用例2：闰年处理
- **输入**: dateRange = ["2024-02-28", "2024-02-29"]
- **期望结果**: 正确处理闰年日期

#### 测试用例3：年末年初跨越
- **输入**: dateRange = ["2023-12-31", "2024-01-01"]
- **期望结果**: 正确处理跨年日期

## 测试步骤

### 前端测试
1. 在浏览器控制台中调用测试方法：
```javascript
// 在SettlementOrders.vue页面的控制台中执行
this.testTimestampFormat("2024-01-15", "2024-01-20");
```

2. 验证时间戳转换结果：
```javascript
const startTimestamp = this.formatDateToTimestamp("2024-01-15", false);
const endTimestamp = this.formatDateToTimestamp("2024-01-20", true);
console.log("开始时间戳:", startTimestamp);
console.log("结束时间戳:", endTimestamp);
console.log("开始时间验证:", new Date(startTimestamp * 1000).toLocaleString());
console.log("结束时间验证:", new Date(endTimestamp * 1000).toLocaleString());
```

### 后端测试
1. 创建测试API调用：
```json
POST /Finance/SettlementOrder/manualGenerateSettlement
{
    "supplierId": 1,
    "settlementStartTime": 1705248000,
    "settlementEndTime": 1705679999,
    "remark": "时间参数测试"
}
```

2. 验证错误处理：
```json
POST /Finance/SettlementOrder/manualGenerateSettlement
{
    "supplierId": 1,
    "settlementStartTime": -1,
    "settlementEndTime": 1705679999,
    "remark": "无效时间戳测试"
}
```

## 预期结果

### 成功场景
- 前端正确转换日期为Unix时间戳
- 后端正确验证时间戳格式
- 时间戳正确转换为日期字符串存储
- 结算单创建成功

### 失败场景
- 无效时间戳被正确拒绝
- 时间范围错误被正确识别
- 返回清晰的错误信息

## 验证要点

1. **时间精度**: 确保开始时间为00:00:00，结束时间为23:59:59
2. **时间戳格式**: 确保为整数类型的Unix时间戳（秒为单位）
3. **数据一致性**: 前后端时间处理逻辑完全一致
4. **错误处理**: 各种异常情况都有适当的错误提示
5. **向后兼容**: 旧的日期字符串格式仍然可用
