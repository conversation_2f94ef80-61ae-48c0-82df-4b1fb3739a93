<?php
/**
 * 结算单模型类
 * @package JinDouYun\Model\Finance
 * <AUTHOR>
 * @date 2023/08/14
 */

namespace JinDouY<PERSON>\Model\Finance;

use <PERSON><PERSON><PERSON><PERSON><PERSON>\Controller\Common\Logger;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\Dao\Finance\DSettlementOrder;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\Dao\Finance\DConsignmentSettlementDetail;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\Dao\Order\DOrder;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\Dao\Order\DOrderGoods;

use <PERSON><PERSON><PERSON><PERSON><PERSON>\Model\MBaseModel;
use JinDouYun\Model\Purchase\MSupplier;
use JinDouYun\Model\Finance\MSettlementCalculate;
use JinD<PERSON>Yun\Model\Finance\MPay;
use Mall\Framework\Core\ErrorCode;
use Mall\Framework\Core\StatusCode;
use Mall\Framework\Core\ResultWrapper;

/**
 * 结算单模型类
 * @package JinDouYun\Model\Finance
 */
class MSettlementOrder extends MBaseModel
{
    /**
     * 结算类型：自动结算
     */
    const SETTLEMENT_TYPE_AUTO = 1;

    /**
     * 结算类型：手动结算
     */
    const SETTLEMENT_TYPE_MANUAL = 2;

    /**
     * 结算状态：待结算
     */
    const SETTLEMENT_STATUS_PENDING = 1;

    /**
     * 结算状态：结算中
     */
    const SETTLEMENT_STATUS_PROCESSING = 2;

    /**
     * 结算状态：已结算
     */
    const SETTLEMENT_STATUS_COMPLETED = 3;

    /**
     * 结算状态：结算失败
     */
    const SETTLEMENT_STATUS_FAILED = 4;

    /**
     * 结算状态：待审核
     */
    const SETTLEMENT_STATUS_PENDING_REVIEW = 5;

    /**
     * 企业ID
     * @var int
     */
    protected $enterpriseId;

    /**
     * 用户ID
     * @var int
     */
    protected $userId;

    /**
     * 结算单数据访问对象
     * @var DSettlementOrder
     */
    protected $objDSettlementOrder;

    /**
     * 结算单明细数据访问对象（重构后使用分账明细表）
     * @var DConsignmentSettlementDetail
     */
    protected $objDConsignmentSettlementDetail;

    /**
     * 订单数据访问对象
     * @var DOrder
     */
    protected $objDOrder;

    /**
     * 订单商品数据访问对象
     * @var DOrderGoods
     */
    protected $objDOrderGoods;

    /**
     * 分账计算模型
     * @var MSettlementCalculate
     */
    protected $objMSettlementCalculate;

    /**
     * 供应商模型
     * @var MSupplier
     */
    protected $objMSupplier;

    /**
     * 应付单模型
     * @var MPay
     */
    protected $objMPay;

    /**
     * 构造函数
     * @param int $enterpriseId 企业ID
     * @param int $userId 用户ID
     */
    public function __construct($enterpriseId, $userId)
    {
        parent::__construct($enterpriseId, $userId);
        $this->enterpriseId = $enterpriseId;
        $this->userId = $userId;

        $this->objDSettlementOrder = new DSettlementOrder();
        $this->objDSettlementOrder->setTable('qianniao_settlement_order_' . $enterpriseId);

        // 重构后使用 DConsignmentSettlementDetail 替代 DSettlementOrderDetails
        $this->objDConsignmentSettlementDetail = new DConsignmentSettlementDetail();
        $this->objDConsignmentSettlementDetail->setTable($enterpriseId);

        $this->objDOrder = new DOrder();
        $this->objDOrder->setTable('qianniao_order_' . $enterpriseId);

        $this->objDOrderGoods = new DOrderGoods();
        $this->objDOrderGoods->setTable('qianniao_order_goods_' . $enterpriseId);

        $this->objMSettlementCalculate = new MSettlementCalculate($enterpriseId, $userId);

        $this->objMSupplier = new MSupplier($userId, $enterpriseId);

        $this->objMPay = new MPay($enterpriseId, $userId);
    }

    /**
     * 创建结算单
     * @param array $data 结算单数据
     * @return ResultWrapper 结果包装器
     */
    public function createSettlementOrder(array $data)
    {
        try {
            // 处理时间戳参数转换
            if (isset($data['settlementStartTime']) && isset($data['settlementEndTime'])) {
                // 转换时间戳为日期字符串（用于数据库存储）
                $data['settlementStartDate'] = date('Y-m-d', $data['settlementStartTime']);
                $data['settlementEndDate'] = date('Y-m-d', $data['settlementEndTime']);
            }

            // 验证数据
            $validateResult = $this->validateSettlementOrderData($data);
            if (!$validateResult->isSuccess()) {
                return $validateResult;
            }

            // 获取供应商信息
            $supplierResult = $this->objMSupplier->getSupplierById($data['supplierId']);
            if (!$supplierResult->isSuccess()) {
                return ResultWrapper::fail('获取供应商信息失败: ' . $supplierResult->getData(), ErrorCode::$paramError);
            }

            $supplierInfo = $supplierResult->getData();
            $supplierName = $supplierInfo['title'] ?? '未知供应商';

            // 开始事务
            $this->objDSettlementOrder->beginTransaction();

            try {
                // 生成结算单号
                $settlementNo = $this->generateSettlementNo();

                // 根据业务逻辑计算结算金额
                $calculationResult = $this->calculateSettlementAmount($data);
                if (!$calculationResult->isSuccess()) {
                    $this->objDSettlementOrder->rollback();
                    return $calculationResult;
                }

                $calculationData = $calculationResult->getData();
                $totalAmount = $calculationData['totalAmount'];
                $actualAmount = $calculationData['actualAmount'];
                $orderCount = $calculationData['orderCount'];

                // 准备结算单数据（符合文档规范）
                $settlementData = [
                    'settlementNo' => $settlementNo,
                    'supplierId' => $data['supplierId'],
                    'supplierName' => $supplierName,
                    'settlementAmount' => $totalAmount, // 结算总金额
                    'actualAmount' => $actualAmount, // 实际结算金额
                    'orderCount' => $orderCount,
                    'settlementType' => $data['settlementType'] ?? self::SETTLEMENT_TYPE_MANUAL,
                    'settlementStatus' => $data['settlementStatus'] ?? self::SETTLEMENT_STATUS_PENDING_REVIEW,
                    'settlementTime' => $data['settlementTime'] ?? null,
                    'settlementStartDate' => $data['settlementStartDate'],
                    'settlementEndDate' => $data['settlementEndDate'],
                    'remark' => $data['remark'] ?? '',
                    'createUserId' => $this->userId,
                    'createUserName' => $data['createUserName'] ?? '',
                    'deleteStatus' => 5, // 正常状态
                ];

                // 添加结算单
                $settlementId = $this->objDSettlementOrder->add($settlementData);
                if ($settlementId === false) {
                    Logger::logs(E_USER_ERROR, '添加结算单失败', __CLASS__, __LINE__, $this->objDSettlementOrder->error());
                    $this->objDSettlementOrder->rollback();
                    return ResultWrapper::fail('创建结算单失败', ErrorCode::$dberror);
                }

                // 回写结算明细数据到寄售结算明细表
                $writeBackResult = $this->writeBackSettlementDetails($settlementId, $settlementNo, $calculationData);
                if (!$writeBackResult->isSuccess()) {
                    Logger::logs(E_USER_WARNING, '回写结算详情数据失败', __CLASS__, __LINE__, [
                        'settlementId' => $settlementId,
                        'settlementNo' => $settlementNo,
                        'error' => $writeBackResult->getData()
                    ]);
                    // 注意：这里不回滚结算单，因为结算单创建成功，回写失败不应该影响主流程
                }

                // 提交事务
                $this->objDSettlementOrder->commit();

                Logger::logs(E_USER_NOTICE, '创建结算单成功', __CLASS__, __LINE__, [
                    'settlementId' => $settlementId,
                    'settlementNo' => $settlementNo,
                    'supplierId' => $data['supplierId'],
                    'totalAmount' => $totalAmount
                ]);

                return ResultWrapper::success([
                    'settlementId' => $settlementId,
                    'settlementNo' => $settlementNo,
                    'totalAmount' => $totalAmount,
                    'actualAmount' => $actualAmount,
                    'orderCount' => $orderCount,
                    'detailCount' => $calculationData['detailCount'] ?? 0
                ]);
            } catch (\Exception $e) {
                $this->objDSettlementOrder->rollback();
                throw $e;
            }
        } catch (\Exception $e) {
            Logger::logs(E_USER_ERROR, '创建结算单异常', __CLASS__, __LINE__, $e->getMessage());
            return ResultWrapper::fail($e->getMessage(), ErrorCode::$systemError);
        }
    }

    /**
     * 获取结算单详情
     * @param int $id 结算单ID
     * @return ResultWrapper 结果包装器
     */
    public function getSettlementDetail(int $id)
    {
        try {
            // 获取结算单基本信息
            $settlement = $this->objDSettlementOrder->findById($id);

            if (!$settlement) {
                return ResultWrapper::fail('结算单不存在', ErrorCode::$contentNotExists);
            }

            // 获取结算单明细
            $details = $this->objDConsignmentSettlementDetail->where([
                'settlementId' => $id,
                'deleteStatus' => 5
            ])->select();

            $settlement['details'] = $details ?: [];

            return ResultWrapper::success($settlement);
        } catch (\Exception $e) {
            Logger::logs(E_USER_ERROR, '获取结算单详情异常', __CLASS__, __LINE__, $e->getMessage());
            return ResultWrapper::fail($e->getMessage(), ErrorCode::$systemError);
        }
    }

    /**
     * 获取结算单列表
     * @param array $conditions 查询条件
     * @param int $page 页码
     * @param int $size 每页记录数
     * @return ResultWrapper 结果包装器
     */
    public function getSettlementList(array $conditions = [], int $page = 0, int $size = 20)
    {
        try {
            // 添加企业ID条件
            // 移除 $conditions['enterpriseId'] = $this->enterpriseId;

            $result = $this->objDSettlementOrder->getList($conditions, $page, $size);

            return ResultWrapper::success($result);
        } catch (\Exception $e) {
            Logger::logs(E_USER_ERROR, '获取结算单列表异常', __CLASS__, __LINE__, $e->getMessage());
            return ResultWrapper::fail($e->getMessage(), ErrorCode::$systemError);
        }
    }

    /**
     * 更新结算单状态
     * @param int $id 结算单ID
     * @param int $status 状态值
     * @param string $remark 备注
     * @return ResultWrapper 结果包装器
     */
    public function updateSettlementStatus(int $id, int $status, string $remark = '')
    {
        try {
            // 获取结算单信息
            $settlement = $this->objDSettlementOrder->findById($id);

            if (!$settlement) {
                return ResultWrapper::fail('结算单不存在', ErrorCode::$contentNotExists);
            }

            // 检查企业ID
            // 移除 if ($settlement['enterpriseId'] != $this->enterpriseId) {
            //     return ResultWrapper::fail('无权操作该结算单', ErrorCode::$noPermission);
            // }

            // 更新状态
            $data = [
                'settlementStatus' => $status,
                'updateUserId' => $this->userId,
                'updateUserName' => '', // 这里可以通过MUser模型获取用户名，简化处理为空
            ];

            if (!empty($remark)) {
                $data['remark'] = $remark;
            }

            $result = $this->objDSettlementOrder->updateById($id, $data);

            if ($result === false) {
                return ResultWrapper::fail($this->objDSettlementOrder->error(), ErrorCode::$dberror);
            }

            return ResultWrapper::success(true);
        } catch (\Exception $e) {
            Logger::logs(E_USER_ERROR, '更新结算单状态异常', __CLASS__, __LINE__, $e->getMessage());
            return ResultWrapper::fail($e->getMessage(), ErrorCode::$systemError);
        }
    }

    /**
     * 审核结算单
     * 审核通过后自动生成应付账单
     * @param int $settlementId 结算单ID
     * @return ResultWrapper 结果包装器
     */
    public function auditSettlementOrder(int $settlementId)
    {
        try {
            // 获取结算单信息
            $settlement = $this->objDSettlementOrder->findById($settlementId);

            if (!$settlement) {
                return ResultWrapper::fail('结算单不存在', ErrorCode::$contentNotExists);
            }

            // 验证结算单状态，只允许审核"待审核"状态的结算单
            if ($settlement['settlementStatus'] != self::SETTLEMENT_STATUS_PENDING_REVIEW) {
                return ResultWrapper::fail('只能审核待审核状态的结算单', ErrorCode::$paramError);
            }

            // 开始事务
            $this->objDSettlementOrder->beginTransaction();

            try {
                // 更新结算单状态为已审核（已结算）
                $updateData = [
                    'settlementStatus' => self::SETTLEMENT_STATUS_PROCESSING,
                    'updateUserId' => $this->userId,
                    'updateTime' => date('Y-m-d H:i:s'),
                    'settlementTime' => date('Y-m-d H:i:s'),
                    'remark' => ($settlement['remark'] ? $settlement['remark'] . '; ' : '') .
                               '审核通过时间: ' . date('Y-m-d H:i:s') . ', 审核人ID: ' . $this->userId
                ];

                $updateResult = $this->objDSettlementOrder->updateById($settlementId, $updateData);
                if ($updateResult === false) {
                    $this->objDSettlementOrder->rollback();
                    return ResultWrapper::fail('更新结算单状态失败', ErrorCode::$dberror);
                }

                // 生成应付账单
                $payableResult = $this->generatePayableFromSettlement($settlement);
                if (!$payableResult->isSuccess()) {
                    $this->objDSettlementOrder->rollback();
                    return $payableResult;
                }

                // 提交事务
                $this->objDSettlementOrder->commit();

                // 记录审核成功日志
                Logger::logs(E_USER_NOTICE, '结算单审核成功', __CLASS__, __LINE__, [
                    'settlementId' => $settlementId,
                    'settlementNo' => $settlement['settlementNo'],
                    'supplierId' => $settlement['supplierId'],
                    'supplierName' => $settlement['supplierName'],
                    'settlementAmount' => $settlement['settlementAmount'],
                    'payableId' => $payableResult->getData()['payableId'] ?? null,
                    'auditUserId' => $this->userId,
                    'auditTime' => date('Y-m-d H:i:s')
                ]);

                return ResultWrapper::success([
                    'settlementId' => $settlementId,
                    'settlementNo' => $settlement['settlementNo'],
                    'payableId' => $payableResult->getData()['payableId'] ?? null,
                    'auditTime' => date('Y-m-d H:i:s')
                ]);

            } catch (\Exception $e) {
                $this->objDSettlementOrder->rollback();
                throw $e;
            }

        } catch (\Exception $e) {
            Logger::logs(E_USER_ERROR, '审核结算单异常', __CLASS__, __LINE__, $e->getMessage());
            return ResultWrapper::fail('审核结算单失败: ' . $e->getMessage(), ErrorCode::$systemError);
        }
    }

    /**
     * 批量审核结算单
     * @param array $settlementIds 结算单ID数组
     * @return ResultWrapper 结果包装器
     */
    public function batchAuditSettlementOrders(array $settlementIds)
    {
        try {
            if (empty($settlementIds)) {
                return ResultWrapper::fail('结算单ID列表不能为空', ErrorCode::$paramError);
            }

            $successCount = 0;
            $failCount = 0;
            $results = [];

            foreach ($settlementIds as $settlementId) {
                try {
                    $result = $this->auditSettlementOrder($settlementId);

                    if ($result->isSuccess()) {
                        $successCount++;
                        $results[] = [
                            'settlementId' => $settlementId,
                            'status' => 'success',
                            'message' => '审核成功',
                            'data' => $result->getData()
                        ];
                    } else {
                        $failCount++;
                        $results[] = [
                            'settlementId' => $settlementId,
                            'status' => 'failed',
                            'message' => $result->getData(),
                            'errorCode' => $result->getErrorCode()
                        ];
                    }
                } catch (\Exception $e) {
                    $failCount++;
                    $results[] = [
                        'settlementId' => $settlementId,
                        'status' => 'error',
                        'message' => '审核异常: ' . $e->getMessage()
                    ];

                    Logger::logs(E_USER_ERROR, '批量审核单个结算单异常', __CLASS__, __LINE__, [
                        'settlementId' => $settlementId,
                        'error' => $e->getMessage()
                    ]);
                }
            }

            return ResultWrapper::success([
                'successCount' => $successCount,
                'failCount' => $failCount,
                'totalCount' => count($settlementIds),
                'results' => $results
            ]);

        } catch (\Exception $e) {
            Logger::logs(E_USER_ERROR, '批量审核结算单异常', __CLASS__, __LINE__, $e->getMessage());
            return ResultWrapper::fail('批量审核结算单失败: ' . $e->getMessage(), ErrorCode::$systemError);
        }
    }

    /**
     * 从结算单生成应付账单
     * @param array $settlement 结算单数据
     * @return ResultWrapper 结果包装器
     */
    private function generatePayableFromSettlement(array $settlement)
    {
        try {
            // 构建应付账单数据
            $payableData = [
                'supplierId' => $settlement['supplierId'],
                'supplierName' => $settlement['supplierName'],
                'PayMoney' => $settlement['settlementAmount'], // 应付金额
                'notOffsetMoney' => $settlement['settlementAmount'], // 未核销金额
                'auditStatus' => StatusCode::$auditStatus['auditing'], // 待审核状态
                'financeTypeId' => 7, // 供应商台账类型
                'financeType' => '供应商分账',
                'sourceId' => $settlement['id'], // 源单ID为结算单ID
                'sourceNo' => $settlement['settlementNo'], // 源单号为结算单号
                'receiptTypeId' => StatusCode::$orderType['settlementOrder'], // 收款类型为结算单
                'createTime' => time(),
                'updateTime' => time(),
                'deleteStatus' => StatusCode::$standard
            ];

            // 调用MPay模型添加应付账单
            $result = $this->objMPay->addPay($payableData);

            if ($result->isSuccess()) {
                return ResultWrapper::success([
                    'payableId' => $result->getData(),
                    'payableAmount' => $settlement['settlementAmount'],
                    'supplierId' => $settlement['supplierId'],
                    'supplierName' => $settlement['supplierName']
                ]);
            } else {
                return ResultWrapper::fail('生成应付账单失败: ' . $result->getData(), $result->getErrorCode());
            }

        } catch (\Exception $e) {
            Logger::logs(E_USER_ERROR, '生成应付账单异常', __CLASS__, __LINE__, $e->getMessage());
            return ResultWrapper::fail('生成应付账单异常: ' . $e->getMessage(), ErrorCode::$systemError);
        }
    }

    /**
     * 执行结算操作（符合文档业务流程）
     * @param int $id 结算单ID
     * @return ResultWrapper 结果包装器
     */
    public function executeSettlement(int $id)
    {
        try {
            // 查询结算单
            $settlement = $this->objDSettlementOrder->findById($id);
            if (!$settlement) {
                return ResultWrapper::fail('结算单不存在', ErrorCode::$paramError);
            }

            // 检查结算单状态
            if ($settlement['settlementStatus'] != self::SETTLEMENT_STATUS_PENDING &&
                $settlement['settlementStatus'] != self::SETTLEMENT_STATUS_PENDING_REVIEW) {
                return ResultWrapper::fail('结算单状态不允许执行结算操作', ErrorCode::$paramError);
            }

            // 开始事务
            $this->objDSettlementOrder->beginTransaction();

            try {
                // 更新结算单状态为结算中
                $updateResult = $this->updateSettlementStatus($id, self::SETTLEMENT_STATUS_PROCESSING);
                if (!$updateResult->isSuccess()) {
                    $this->objDSettlementOrder->rollback();
                    return $updateResult;
                }

                // 执行资金划拨操作
                // 这里应该调用资金划拨服务，将结算金额划拨给供应商
                // 简化处理，直接更新状态为已结算
                $completeResult = $this->completeSettlementOrder($id);
                if (!$completeResult->isSuccess()) {
                    $this->objDSettlementOrder->rollback();
                    return $completeResult;
                }

                // 提交事务
                $this->objDSettlementOrder->commit();

                return ResultWrapper::success('结算操作执行成功');
            } catch (\Exception $e) {
                $this->objDSettlementOrder->rollback();
                throw $e;
            }
        } catch (\Exception $e) {
            // 更新结算单状态为结算失败
            $this->updateSettlementStatus($id, self::SETTLEMENT_STATUS_FAILED, '结算失败：' . $e->getMessage());

            Logger::logs(E_USER_ERROR, '执行结算操作异常', __CLASS__, __LINE__, $e->getMessage());
            return ResultWrapper::fail('执行结算操作失败：' . $e->getMessage(), ErrorCode::$systemError);
        }
    }

    /**
     * 完成结算单（符合文档业务流程）
     * @param int $settlementOrderId 结算单ID
     * @return ResultWrapper 结果包装器
     */
    public function completeSettlementOrder($settlementOrderId)
    {
        try {
            if (empty($settlementOrderId)) {
                return ResultWrapper::fail('结算单ID不能为空', ErrorCode::$paramError);
            }

            // 查询结算单信息
            $settlementOrder = $this->objDSettlementOrder->findById($settlementOrderId);
            if (empty($settlementOrder)) {
                return ResultWrapper::fail('结算单不存在', ErrorCode::$contentNotExists);
            }

            if ($settlementOrder['settlementStatus'] == self::SETTLEMENT_STATUS_COMPLETED) {
                return ResultWrapper::fail('结算单已完成，不能重复操作', ErrorCode::$paramError);
            }

            // 更新结算单状态
            $updateData = [
                'settlementStatus' => self::SETTLEMENT_STATUS_COMPLETED,
                'settlementTime' => date('Y-m-d H:i:s'),
                'updateTime' => date('Y-m-d H:i:s'),
                'updateUserId' => $this->userId
            ];

            $updateResult = $this->objDSettlementOrder->updateById($settlementOrderId, $updateData);
            if (!$updateResult) {
                return ResultWrapper::fail('更新结算单状态失败', ErrorCode::$dberror);
            }

            // 同步更新关联的分账明细状态（符合文档流程第6步）
            $updateDetailResult = $this->batchUpdateSettlementDetailsBySettlementId(
                $settlementOrderId,
                self::SETTLEMENT_STATUS_COMPLETED,
                date('Y-m-d H:i:s')
            );

            if (!$updateDetailResult->isSuccess()) {
                // 记录错误日志，但不回滚结算单状态
                Logger::logs(E_USER_ERROR, '更新分账明细状态失败', __CLASS__, __LINE__, [
                    'settlementOrderId' => $settlementOrderId,
                    'error' => $updateDetailResult->getData()
                ]);
            }

            Logger::logs(E_USER_NOTICE, '结算单完成成功', __CLASS__, __LINE__, [
                'settlementOrderId' => $settlementOrderId,
                'settlementNo' => $settlementOrder['settlementNo']
            ]);

            return ResultWrapper::success('结算单完成成功');
        } catch (\Exception $e) {
            Logger::logs(E_USER_ERROR, '完成结算单异常', __CLASS__, __LINE__, $e->getMessage());
            return ResultWrapper::fail('完成结算单失败: ' . $e->getMessage(), ErrorCode::$systemError);
        }
    }

    /**
     * 获取供应商结算流水记录（供应商角色端专用）
     * 任务11：结算流水查询模块 - 后端
     *
     * @param array $conditions 查询条件
     * @param int $page 页码
     * @param int $pageSize 每页数量
     * @return ResultWrapper 结算流水记录
     */
    public function getSupplierSettlementRecords($conditions, $page = 1, $pageSize = 10): ResultWrapper
    {
        try {
            // 验证必要参数
            if (empty($conditions['supplierId'])) {
                return ResultWrapper::fail('供应商ID不能为空', ErrorCode::$paramError);
            }

            // 构建查询条件
            $where = "supplierId = " . intval($conditions['supplierId']) . " AND deleteStatus = 5";

            // 添加结算月份条件
            if (!empty($conditions['settlementMonth'])) {
                $month = $conditions['settlementMonth'];
                $where .= " AND DATE_FORMAT(FROM_UNIXTIME(settlementTime), '%Y-%m') = '" . $month . "'";
            }

            // 添加结算状态条件
            if (isset($conditions['settlementStatus'])) {
                $where .= " AND settlementStatus = " . intval($conditions['settlementStatus']);
            }

            // 获取总数
            $total = $this->objDSettlementOrder->count($where);
            if ($total === false) {
                return ResultWrapper::fail('获取结算记录总数失败：' . $this->objDSettlementOrder->error(), ErrorCode::$dberror);
            }

            // 计算分页参数
            $limit = $pageSize;
            $offset = ($page - 1) * $pageSize;

            // 获取结算记录列表（已移除税费字段）
            $fields = 'id, settlementNo, settlementAmount, actualAmount, settlementStatus, settlementTime, settlementStartDate, settlementEndDate, remark, createTime';
            $records = $this->objDSettlementOrder->select($where, $fields, 'settlementTime DESC', $limit, $offset);

            if ($records === false) {
                return ResultWrapper::fail('获取结算记录失败：' . $this->objDSettlementOrder->error(), ErrorCode::$dberror);
            }

            // 格式化数据
            $formattedRecords = $this->formatSupplierSettlementRecords($records);

            return ResultWrapper::success([
                'list' => $formattedRecords,
                'total' => $total
            ]);
        } catch (\Exception $e) {
            Logger::logs(E_USER_ERROR, '获取供应商结算流水记录异常', __CLASS__, __LINE__, $e->getMessage());
            return ResultWrapper::fail('获取结算流水记录失败：' . $e->getMessage(), ErrorCode::$systemError);
        }
    }

    /**
     * 格式化供应商结算记录数据
     *
     * @param array $records 原始结算记录
     * @return array 格式化后的记录
     */
    private function formatSupplierSettlementRecords($records)
    {
        $formattedRecords = [];

        foreach ($records as $record) {
            $formattedRecord = [
                'id' => $record['id'],
                'settlementNo' => $record['settlementNo'],
                'settlementAmount' => floatval($record['settlementAmount']),
                'actualAmount' => floatval($record['actualAmount']),
                'settlementStatus' => intval($record['settlementStatus']),
                'settlementStatusName' => $this->getSettlementStatusName($record['settlementStatus']),
                'settlementTime' => $record['settlementTime'],
                'settlementStartDate' => $record['settlementStartDate'],
                'settlementEndDate' => $record['settlementEndDate'],
                'settlementPeriod' => $record['settlementStartDate'] . ' 至 ' . $record['settlementEndDate'],
                'remark' => $record['remark'],
                'createTime' => $record['createTime'],
            ];

            $formattedRecords[] = $formattedRecord;
        }

        return $formattedRecords;
    }

    /**
     * 获取结算状态名称
     *
     * @param int $status 结算状态
     * @return string 状态名称
     */
    private function getSettlementStatusName($status)
    {
        $statusNames = [
            self::SETTLEMENT_STATUS_PENDING => '待结算',
            self::SETTLEMENT_STATUS_PROCESSING => '结算中',
            self::SETTLEMENT_STATUS_COMPLETED => '已结算',
            self::SETTLEMENT_STATUS_FAILED => '结算失败',
            self::SETTLEMENT_STATUS_PENDING_REVIEW => '待审核',
        ];

        return $statusNames[$status] ?? '未知状态';
    }

    /**
     * 获取供应商结算汇总数据（供应商角色端专用）
     * 任务11：结算流水查询模块 - 后端
     *
     * @param array $conditions 查询条件
     * @return ResultWrapper 汇总数据
     */
    public function getSupplierSettlementSummary($conditions): ResultWrapper
    {
        try {
            // 验证必要参数
            if (empty($conditions['supplierId'])) {
                return ResultWrapper::fail('供应商ID不能为空', ErrorCode::$paramError);
            }

            // 构建查询条件
            $where = "supplierId = " . intval($conditions['supplierId']) . " AND deleteStatus = 5";

            // 添加结算月份条件
            if (!empty($conditions['settlementMonth'])) {
                $month = $conditions['settlementMonth'];
                $where .= " AND DATE_FORMAT(FROM_UNIXTIME(settlementTime), '%Y-%m') = '" . $month . "'";
            }

            // 获取汇总统计
            $tableName = 'qianniao_settlement_order_' . $this->enterpriseId;
            $sql = "SELECT
                        COUNT(*) as totalCount,
                        SUM(CASE WHEN settlementStatus = " . self::SETTLEMENT_STATUS_COMPLETED . " THEN settlementAmount ELSE 0 END) as settledAmount,
                        SUM(CASE WHEN settlementStatus IN (" . self::SETTLEMENT_STATUS_PENDING . "," . self::SETTLEMENT_STATUS_PENDING_REVIEW . ") THEN settlementAmount ELSE 0 END) as pendingAmount,
                        SUM(settlementAmount) as totalAmount
                    FROM {$tableName}
                    WHERE {$where}";

            $result = $this->objDSettlementOrder->query($sql);

            if ($result === false) {
                return ResultWrapper::fail('获取汇总数据失败：' . $this->objDSettlementOrder->error(), ErrorCode::$dberror);
            }

            $summaryData = $result[0] ?? [];

            // 格式化数据
            $formattedData = [
                'totalCount' => intval($summaryData['totalCount'] ?? 0),
                'settledAmount' => floatval($summaryData['settledAmount'] ?? 0),
                'pendingAmount' => floatval($summaryData['pendingAmount'] ?? 0),
                'totalAmount' => floatval($summaryData['totalAmount'] ?? 0),
            ];

            return ResultWrapper::success($formattedData);
        } catch (\Exception $e) {
            Logger::logs(E_USER_ERROR, '获取供应商结算汇总数据异常', __CLASS__, __LINE__, $e->getMessage());
            return ResultWrapper::fail('获取结算汇总数据失败：' . $e->getMessage(), ErrorCode::$systemError);
        }
    }

    /**
     * 验证结算单数据
     * @param array $data 结算单数据
     * @return ResultWrapper 结果包装器
     */
    private function validateSettlementOrderData($data)
    {
        try {
            // 验证必要字段
            if (empty($data['supplierId'])) {
                return ResultWrapper::fail('供应商ID不能为空', ErrorCode::$paramError);
            }

            // 验证数据类型
            if (!is_numeric($data['supplierId']) || $data['supplierId'] <= 0) {
                return ResultWrapper::fail('供应商ID必须为正整数', ErrorCode::$paramError);
            }

            // 支持新的时间戳参数和旧的日期字符串参数
            if (isset($data['settlementStartTime']) && isset($data['settlementEndTime'])) {
                // 验证时间戳格式
                if (!$this->validateTimestamp($data['settlementStartTime']) || !$this->validateTimestamp($data['settlementEndTime'])) {
                    return ResultWrapper::fail('时间戳格式不正确，必须为有效的Unix时间戳（整数，秒为单位）', ErrorCode::$paramError);
                }

                // 验证时间范围
                if ($data['settlementStartTime'] >= $data['settlementEndTime']) {
                    return ResultWrapper::fail('结算开始时间必须小于结束时间', ErrorCode::$paramError);
                }

                // 转换时间戳为日期字符串（用于数据库存储）
                // 注意：这里需要通过引用传递来修改原始数据
                // 但由于PHP的限制，我们在调用方法中处理这个转换
            }
            else if (isset($data['settlementStartDate']) && isset($data['settlementEndDate'])) {
                // 兼容旧的日期字符串格式
                if (empty($data['settlementStartDate']) || empty($data['settlementEndDate'])) {
                    return ResultWrapper::fail('结算周期不能为空', ErrorCode::$paramError);
                }

                // 验证日期格式
                if (!strtotime($data['settlementStartDate']) || !strtotime($data['settlementEndDate'])) {
                    return ResultWrapper::fail('结算日期格式不正确', ErrorCode::$paramError);
                }

                // 验证日期逻辑
                if (strtotime($data['settlementStartDate']) >= strtotime($data['settlementEndDate'])) {
                    return ResultWrapper::fail('结算开始日期必须小于结束日期', ErrorCode::$paramError);
                }
            } else {
                return ResultWrapper::fail('结算周期不能为空，请提供settlementStartTime和settlementEndTime参数', ErrorCode::$paramError);
            }

            // 验证金额字段
            if (isset($data['settlementAmount']) && (!is_numeric($data['settlementAmount']) || $data['settlementAmount'] < 0)) {
                return ResultWrapper::fail('结算金额不能为负数', ErrorCode::$paramError);
            }

            return ResultWrapper::success(true);
        } catch (\Exception $e) {
            Logger::logs(E_USER_ERROR, '结算单数据验证异常', __CLASS__, __LINE__, [
                'data' => $data,
                'exception' => $e->getMessage()
            ]);
            return ResultWrapper::fail('数据验证异常: ' . $e->getMessage(), ErrorCode::$systemError);
        }
    }







    /**
     * 创建供应商仓储结算单（供应商角色端专用）
     * 基于固定价格计算，简化结算流程
     * @param array $data 结算单数据
     * @return ResultWrapper 结果包装器
     */
    public function createSupplierStorageSettlement(array $data)
    {
        try {
            // 处理时间戳参数转换
            if (isset($data['settlementStartTime']) && isset($data['settlementEndTime'])) {
                // 转换时间戳为日期字符串（用于数据库存储）
                $data['settlementStartDate'] = date('Y-m-d', $data['settlementStartTime']);
                $data['settlementEndDate'] = date('Y-m-d', $data['settlementEndTime']);
            }

            // 验证数据
            $validateResult = $this->validateSupplierStorageSettlementData($data);
            if (!$validateResult->isSuccess()) {
                return $validateResult;
            }

            // 获取供应商信息
            $supplierResult = $this->objMSupplier->getSupplierById($data['supplierId']);
            if (!$supplierResult->isSuccess()) {
                return ResultWrapper::fail('获取供应商信息失败: ' . $supplierResult->getData(), ErrorCode::$paramError);
            }

            $supplierInfo = $supplierResult->getData();
            $supplierName = $supplierInfo['title'] ?? '未知供应商';

            // 开始事务
            $this->objDSettlementOrder->beginTransaction();

            try {
                // 生成结算单号
                $settlementNo = $this->generateSettlementNo();

                // 基于固定价格计算结算金额
                $calculationResult = $this->calculateSupplierStorageSettlementAmount($data);
                if (!$calculationResult->isSuccess()) {
                    $this->objDSettlementOrder->rollback();
                    return $calculationResult;
                }

                $calculationData = $calculationResult->getData();
                $totalAmount = $calculationData['totalAmount'];
                $actualAmount = $calculationData['actualAmount'];
                $orderCount = $calculationData['orderCount'];

                // 准备结算单数据
                $settlementData = [
                    'settlementNo' => $settlementNo,
                    'supplierId' => $data['supplierId'],
                    'supplierName' => $supplierName,
                    'settlementAmount' => $totalAmount,
                    'actualAmount' => $actualAmount,
                    'orderCount' => $orderCount,
                    'settlementType' => $data['settlementType'] ?? self::SETTLEMENT_TYPE_MANUAL,
                    'settlementStatus' => self::SETTLEMENT_STATUS_PENDING_REVIEW, // 待审核状态
                    'settlementTime' => null,
                    'settlementStartDate' => $data['settlementStartDate'],
                    'settlementEndDate' => $data['settlementEndDate'],
                    'remark' => $data['remark'] ?? '',
                    'createUserId' => $this->userId,
                    'createUserName' => $data['createUserName'] ?? '',
                    'deleteStatus' => 5, // 正常状态
                ];

                // 添加结算单
                $settlementId = $this->objDSettlementOrder->add($settlementData);
                if ($settlementId === false) {
                    Logger::logs(E_USER_ERROR, '添加供应商仓储结算单失败', __CLASS__, __LINE__, $this->objDSettlementOrder->error());
                    $this->objDSettlementOrder->rollback();
                    return ResultWrapper::fail('创建供应商仓储结算单失败', ErrorCode::$dberror);
                }

                // 回写结算明细数据
                $writeBackResult = $this->writeBackSupplierStorageSettlementDetails($settlementId, $settlementNo, $calculationData);
                if (!$writeBackResult->isSuccess()) {
                    Logger::logs(E_USER_WARNING, '回写供应商仓储结算详情数据失败', __CLASS__, __LINE__, [
                        'settlementId' => $settlementId,
                        'settlementNo' => $settlementNo,
                        'error' => $writeBackResult->getData()
                    ]);
                    // 注意：这里不回滚结算单，因为结算单创建成功，回写失败不应该影响主流程
                }

                // 提交事务
                $this->objDSettlementOrder->commit();

                Logger::logs(E_USER_NOTICE, '创建供应商仓储结算单成功', __CLASS__, __LINE__, [
                    'settlementId' => $settlementId,
                    'settlementNo' => $settlementNo,
                    'supplierId' => $data['supplierId'],
                    'totalAmount' => $totalAmount
                ]);

                return ResultWrapper::success([
                    'settlementId' => $settlementId,
                    'settlementNo' => $settlementNo,
                    'totalAmount' => $totalAmount,
                    'actualAmount' => $actualAmount,
                    'orderCount' => $orderCount,
                    'detailCount' => $calculationData['detailCount'] ?? 0,
                    'settlementStatus' => self::SETTLEMENT_STATUS_PENDING_REVIEW
                ]);
            } catch (\Exception $e) {
                $this->objDSettlementOrder->rollback();
                throw $e;
            }
        } catch (\Exception $e) {
            Logger::logs(E_USER_ERROR, '创建供应商仓储结算单异常', __CLASS__, __LINE__, $e->getMessage());
            return ResultWrapper::fail($e->getMessage(), ErrorCode::$systemError);
        }
    }

    /**
     * 手动触发结算单生成预览
     * @param array $params 生成参数
     * @return ResultWrapper 结果包装器
     */
    public function previewSettlementGeneration($params)
    {
        try {
            Logger::logs(E_USER_NOTICE, '开始预览结算单生成', __CLASS__, __LINE__, [
                'supplierId' => $params['supplierId'] ?? null,
                'startDate' => $params['startDate'] ?? null,
                'endDate' => $params['endDate'] ?? null
            ]);

            // 验证参数
            if (empty($params['supplierId'])) {
                return ResultWrapper::fail('供应商ID不能为空', ErrorCode::$paramError);
            }

            if (empty($params['startDate']) || empty($params['endDate'])) {
                return ResultWrapper::fail('结算时间范围不能为空', ErrorCode::$paramError);
            }

            // 查询该供应商在指定时间范围内的分账明细
            $objMConsignmentSettlementDetail = new MConsignmentSettlementDetail($this->enterpriseId, $this->userId);

            $detailParams = [
                'supplierId' => $params['supplierId'],
                'startDate' => $params['startDate'],
                'endDate' => $params['endDate'],
                'settlementStatus' => 1, // 只查询待结算的明细
            ];

            $detailResult = $objMConsignmentSettlementDetail->getSettlementDetailList($detailParams);
            if (!$detailResult->isSuccess()) {
                return ResultWrapper::fail('获取分账明细失败: ' . $detailResult->getData(), ErrorCode::$systemError);
            }

            $detailData = $detailResult->getData();
            $detailList = $detailData['list'] ?? [];

            // 检查是否存在重复结算
            $duplicateCheckResult = $this->checkDuplicateSettlement($params['supplierId'], $params['startDate'], $params['endDate']);
            if (!$duplicateCheckResult->isSuccess()) {
                return $duplicateCheckResult;
            }

            // 计算预览数据
            $totalSettlementAmount = 0;
            $orderCount = 0;
            $orderIds = [];
            $materielCount = 0;
            $materielIds = [];

            foreach ($detailList as $detail) {
                $totalSettlementAmount += floatval($detail['settlementAmount']);

                if (!in_array($detail['orderId'], $orderIds)) {
                    $orderIds[] = $detail['orderId'];
                    $orderCount++;
                }

                if (!in_array($detail['materielId'], $materielIds)) {
                    $materielIds[] = $detail['materielId'];
                    $materielCount++;
                }
            }

            $actualAmount = $totalSettlementAmount; // 实际金额等于结算金额（已移除税费）

            // 获取供应商信息
            $supplierResult = $this->objMSupplier->getSupplierById($params['supplierId']);
            $supplierInfo = $supplierResult->isSuccess() ? $supplierResult->getData() : [];

            $previewData = [
                'supplierInfo' => [
                    'id' => $params['supplierId'],
                    'name' => $supplierInfo['title'] ?? '未知供应商',
                ],
                'settlementPeriod' => [
                    'startDate' => $params['startDate'],
                    'endDate' => $params['endDate'],
                ],
                'summary' => [
                    'totalSettlementAmount' => $totalSettlementAmount,
                    'actualAmount' => $actualAmount,
                    'orderCount' => $orderCount,
                    'materielCount' => $materielCount,
                    'detailCount' => count($detailList),
                ],
                'details' => array_slice($detailList, 0, 10), // 只返回前10条明细用于预览
                'hasMore' => count($detailList) > 10,
            ];

            Logger::logs(E_USER_NOTICE, '结算单生成预览完成', __CLASS__, __LINE__, [
                'supplierId' => $params['supplierId'],
                'totalAmount' => $totalSettlementAmount,
                'detailCount' => count($detailList)
            ]);

            return ResultWrapper::success($previewData);
        } catch (\Exception $e) {
            Logger::logs(E_USER_ERROR, '预览结算单生成异常', __CLASS__, __LINE__, [
                'params' => $params,
                'exception' => $e->getMessage()
            ]);
            return ResultWrapper::fail('预览结算单生成异常: ' . $e->getMessage(), ErrorCode::$systemError);
        }
    }

    /**
     * 检查重复结算
     * @param int $supplierId 供应商ID
     * @param string $startDate 开始日期
     * @param string $endDate 结束日期
     * @return ResultWrapper 结果包装器
     */
    private function checkDuplicateSettlement($supplierId, $startDate, $endDate)
    {
        try {
            $where = [
                'supplierId' => $supplierId,
                'settlementStartDate' => $startDate,
                'settlementEndDate' => $endDate,
                'deleteStatus' => 5,
            ];

            $existingSettlement = $this->objDSettlementOrder->where($where)->find();

            if ($existingSettlement) {
                Logger::logs(E_USER_WARNING, '发现重复结算单', __CLASS__, __LINE__, [
                    'supplierId' => $supplierId,
                    'startDate' => $startDate,
                    'endDate' => $endDate,
                    'existingSettlementId' => $existingSettlement['id']
                ]);
                return ResultWrapper::fail('该供应商在此时间范围内已存在结算单，请检查后重试', ErrorCode::$paramError);
            }

            return ResultWrapper::success(true);
        } catch (\Exception $e) {
            Logger::logs(E_USER_ERROR, '检查重复结算异常', __CLASS__, __LINE__, [
                'supplierId' => $supplierId,
                'startDate' => $startDate,
                'endDate' => $endDate,
                'exception' => $e->getMessage()
            ]);
            return ResultWrapper::fail('检查重复结算异常: ' . $e->getMessage(), ErrorCode::$systemError);
        }
    }

    /**
     * 生成结算单号
     *
     * 单号生成规则：
     * - 前缀：ST（Settlement的缩写，体现结算单特征）
     * - 时间戳：YYYYMMDD（8位年月日格式，保证可读性）
     * - 序列号：4位数字（0001-9999），当天从1开始自增
     * - 格式：ST20231214-0001
     *
     * 唯一性保证：
     * - 基于数据库查询当天最后一条记录
     * - 序列号按天递增，跨天自动重置
     * - 使用事务保证并发安全
     *
     * @return string 结算单号
     * @throws \Exception 当生成单号失败时抛出异常
     */
    private function generateSettlementNo()
    {
        try {
            // 查询当天最后一条结算单记录，获取最大单号
            $todayStart = strtotime(date('Ymd') . ' 00:00:00');
            $todayEnd = strtotime(date('Ymd') . ' 23:59:59');

            $lastRecord = $this->objDSettlementOrder->where([
                'createTime' => ['between', [$todayStart, $todayEnd]]
            ])->order('settlementNo DESC')->find();

            $maxNo = '';
            if ($lastRecord && !empty($lastRecord['settlementNo'])) {
                $maxNo = $lastRecord['settlementNo'];
            }

            // 使用统一的单号生成函数，传入结算单前缀
            $serialNo = $this->createSettlementSerialNumber($maxNo);

            return $serialNo;
        } catch (\Exception $e) {
            Logger::logs(E_USER_ERROR, '生成结算单号异常', __CLASS__, __LINE__, [
                'exception' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw new \Exception('生成结算单号失败: ' . $e->getMessage());
        }
    }

    /**
     * 创建结算单序列号
     *
     * 参考 createSerialNumberByDate 函数的实现模式
     * 适配结算单业务场景的单号生成规则
     *
     * @param string $maxNo 当天最后一条结算单号
     * @return string 新的结算单号
     */
    private function createSettlementSerialNumber($maxNo = '')
    {
        $number = 0;

        // 解析当天最大单号，提取序列号部分
        if (!empty($maxNo)) {
            // 结算单号格式：ST20231214-0001
            // 提取ST前缀后的部分：20231214-0001
            $noParts = substr($maxNo, 2); // 去掉ST前缀
            $todayLastNo = explode('-', $noParts);

            if (count($todayLastNo) >= 2) {
                $lastDate = $todayLastNo[0]; // 日期部分
                $lastNumber = intval($todayLastNo[1]); // 序列号部分

                // 检查是否为当天的单号
                $currentDate = date('Ymd');
                if ($lastDate === $currentDate) {
                    $number = $lastNumber;
                }
            }
        }

        // 序列号递增
        $number = $number + 1;

        // 生成新的结算单号
        $currentDate = date('Ymd');
        $serialNumber = str_pad($number, 4, '0', STR_PAD_LEFT);

        return $currentDate . '-' . $serialNumber;
    }

    /**
     * 计算结算金额（基于分账明细汇总）
     * @param array $data 结算单数据
     * @return ResultWrapper 结果包装器
     */
    private function calculateSettlementAmount($data)
    {
        try {
            // 如果直接传入了金额，则使用传入的金额
            if (isset($data['settlementAmount']) && $data['settlementAmount'] > 0) {
                return ResultWrapper::success([
                    'totalAmount' => floatval($data['settlementAmount']),
                    'actualAmount' => floatval($data['actualAmount'] ?? $data['settlementAmount']),
                    'orderCount' => intval($data['orderCount'] ?? 0)
                ]);
            }

            // 否则基于分账明细计算
            $objMConsignmentSettlementDetail = new MConsignmentSettlementDetail($this->enterpriseId, $this->userId);

            $detailParams = [
                'supplierId' => $data['supplierId'],
                'startDate' => $data['settlementStartDate'],
                'endDate' => $data['settlementEndDate'],
                'settlementStatus' => 1, // 只查询待结算的明细
            ];

            $detailResult = $objMConsignmentSettlementDetail->getSettlementDetailList($detailParams);
            if (!$detailResult->isSuccess()) {
                return ResultWrapper::fail('获取分账明细失败: ' . $detailResult->getData(), ErrorCode::$systemError);
            }

            $detailData = $detailResult->getData();
            $detailList = $detailData['list'] ?? [];

            // 计算汇总数据
            $totalAmount = 0;
            $orderCount = 0;
            $orderIds = [];

            foreach ($detailList as $detail) {
                $totalAmount += floatval($detail['settlementAmount']);

                if (!in_array($detail['orderId'], $orderIds)) {
                    $orderIds[] = $detail['orderId'];
                    $orderCount++;
                }
            }

            $actualAmount = $totalAmount; // 实际金额等于结算金额（已移除税费）

            return ResultWrapper::success([
                'totalAmount' => $totalAmount,
                'actualAmount' => $actualAmount,
                'orderCount' => $orderCount,
                'detailCount' => count($detailList),
                'detailList' => $detailList // 添加明细列表用于回写
            ]);
        } catch (\Exception $e) {
            Logger::logs(E_USER_ERROR, '计算结算金额异常', __CLASS__, __LINE__, $e->getMessage());
            return ResultWrapper::fail('计算结算金额失败: ' . $e->getMessage(), ErrorCode::$systemError);
        }
    }



    /**
     * 批量更新结算单关联的分账明细状态
     * @param int $settlementId 结算单ID
     * @param int $status 新状态
     * @param string $settlementTime 结算时间
     * @return ResultWrapper 结果包装器
     */
    private function batchUpdateSettlementDetailsBySettlementId($settlementId, $status, $settlementTime)
    {
        try {
            // 构建更新条件
            $where = [
                'settlementId' => $settlementId,
                'deleteStatus' => 5
            ];

            // 构建更新数据
            $updateData = [
                'settlementStatus' => $status,
                'updateTime' => date('Y-m-d H:i:s')
            ];

            if ($status == self::SETTLEMENT_STATUS_COMPLETED) {
                $updateData['settlementTime'] = $settlementTime;
            }

            // 使用DAO层直接执行批量更新
            $objDConsignmentSettlementDetail = new \JinDouYun\Dao\Finance\DConsignmentSettlementDetail();
            $objDConsignmentSettlementDetail->setTable($this->enterpriseId);

            $result = $objDConsignmentSettlementDetail->where($where)->update($updateData);

            if ($result === false) {
                Logger::logs(E_USER_ERROR, '批量更新分账明细状态失败', __CLASS__, __LINE__, [
                    'settlementId' => $settlementId,
                    'status' => $status,
                    'error' => $objDConsignmentSettlementDetail->error()
                ]);
                return ResultWrapper::fail('批量更新分账明细状态失败: ' . $objDConsignmentSettlementDetail->error(), ErrorCode::$dberror);
            }

            Logger::logs(E_USER_NOTICE, '批量更新分账明细状态成功', __CLASS__, __LINE__, [
                'settlementId' => $settlementId,
                'status' => $status,
                'affectedRows' => $result
            ]);

            return ResultWrapper::success([
                'affectedRows' => $result
            ]);
        } catch (\Exception $e) {
            Logger::logs(E_USER_ERROR, '批量更新分账明细状态异常', __CLASS__, __LINE__, [
                'settlementId' => $settlementId,
                'status' => $status,
                'exception' => $e->getMessage()
            ]);
            return ResultWrapper::fail('批量更新分账明细状态异常: ' . $e->getMessage(), ErrorCode::$systemError);
        }
    }

    /**
     * 回写结算详情数据到寄售结算明细表
     * 通过UPDATE操作更新现有分账明细记录中的结算相关字段
     * @param int $settlementOrderId 结算单ID
     * @param string $settlementNo 结算单号
     * @param array $calculationData 计算数据（包含明细列表）
     * @return ResultWrapper 结果包装器
     */
    private function writeBackSettlementDetails($settlementOrderId, $settlementNo, $calculationData)
    {
        try {
            Logger::logs(E_USER_NOTICE, '开始回写结算详情数据到现有分账明细记录', __CLASS__, __LINE__, [
                'settlementOrderId' => $settlementOrderId,
                'settlementNo' => $settlementNo,
                'detailCount' => isset($calculationData['detailList']) ? count($calculationData['detailList']) : 0
            ]);

            // 获取结算明细数据
            $detailList = $this->getSettlementDetailListForWriteBack($settlementOrderId, $settlementNo, $calculationData);
            if (empty($detailList)) {
                Logger::logs(E_USER_WARNING, '没有找到需要回写的结算明细数据', __CLASS__, __LINE__, [
                    'settlementOrderId' => $settlementOrderId,
                    'calculationData' => $calculationData
                ]);
                return ResultWrapper::success('没有需要回写的明细数据');
            }

            // 在Model层实现批量回写逻辑，通过UPDATE操作更新现有记录
            $result = $this->batchWriteBackSettlementDetailsInModel(
                $settlementOrderId,
                $settlementNo,
                $detailList
            );

            if (!$result) {
                Logger::logs(E_USER_ERROR, '批量回写结算详情数据失败', __CLASS__, __LINE__, [
                    'settlementOrderId' => $settlementOrderId,
                    'settlementNo' => $settlementNo,
                    'detailCount' => count($detailList),
                    'error' => $this->objDConsignmentSettlementDetail->error()
                ]);
                return ResultWrapper::fail('批量回写结算详情数据失败: ' . $this->objDConsignmentSettlementDetail->error(), ErrorCode::$dberror);
            }

            Logger::logs(E_USER_NOTICE, '回写结算详情数据成功', __CLASS__, __LINE__, [
                'settlementOrderId' => $settlementOrderId,
                'settlementNo' => $settlementNo,
                'detailCount' => count($detailList)
            ]);

            return ResultWrapper::success('回写结算详情数据成功');
        } catch (\Exception $e) {
            Logger::logs(E_USER_ERROR, '回写结算详情数据异常', __CLASS__, __LINE__, [
                'settlementOrderId' => $settlementOrderId,
                'settlementNo' => $settlementNo,
                'exception' => $e->getMessage()
            ]);
            return ResultWrapper::fail('回写结算详情数据异常: ' . $e->getMessage(), ErrorCode::$systemError);
        }
    }

    /**
     * 获取用于回写的结算明细数据列表
     * @param int $settlementOrderId 结算单ID
     * @param string $settlementNo 结算单号
     * @param array $calculationData 计算数据
     * @return array 结算明细数据列表
     */
    private function getSettlementDetailListForWriteBack($settlementOrderId, $settlementNo, $calculationData)
    {
        try {
            // 如果计算数据中已包含明细列表，直接使用
            if (isset($calculationData['detailList']) && !empty($calculationData['detailList'])) {
                return $this->formatDetailListForWriteBack($calculationData['detailList'], $settlementOrderId, $settlementNo);
            }

            // 否则重新查询分账明细数据
            $objMConsignmentSettlementDetail = new MConsignmentSettlementDetail($this->enterpriseId, $this->userId);

            // 从结算单数据中获取查询参数
            $settlement = $this->objDSettlementOrder->findById($settlementOrderId);
            if (empty($settlement)) {
                Logger::logs(E_USER_ERROR, '获取结算单信息失败', __CLASS__, __LINE__, [
                    'settlementOrderId' => $settlementOrderId
                ]);
                return [];
            }

            $detailParams = [
                'supplierId' => $settlement['supplierId'],
                'startDate' => $settlement['settlementStartDate'],
                'endDate' => $settlement['settlementEndDate'],
                'settlementStatus' => 1, // 只查询待结算的明细
            ];

            $detailResult = $objMConsignmentSettlementDetail->getSettlementDetailList($detailParams);
            if (!$detailResult->isSuccess()) {
                Logger::logs(E_USER_ERROR, '获取分账明细失败', __CLASS__, __LINE__, [
                    'params' => $detailParams,
                    'error' => $detailResult->getData()
                ]);
                return [];
            }

            $detailData = $detailResult->getData();
            $detailList = $detailData['list'] ?? [];

            return $this->formatDetailListForWriteBack($detailList, $settlementOrderId, $settlementNo);
        } catch (\Exception $e) {
            Logger::logs(E_USER_ERROR, '获取回写明细数据异常', __CLASS__, __LINE__, [
                'settlementOrderId' => $settlementOrderId,
                'exception' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * 格式化明细数据用于回写
     * @param array $detailList 原始明细数据
     * @param int $settlementOrderId 结算单ID
     * @param string $settlementNo 结算单号
     * @return array 格式化后的明细数据
     */
    private function formatDetailListForWriteBack($detailList, $settlementOrderId, $settlementNo)
    {
        $formattedList = [];

        foreach ($detailList as $detail) {
            // 验证必要字段
            if (empty($detail['skuId']) || empty($detail['supplierId'])) {
                Logger::logs(E_USER_WARNING, '跳过无效的明细数据', __CLASS__, __LINE__, [
                    'detail' => $detail,
                    'reason' => 'skuId或supplierId为空'
                ]);
                continue;
            }

            // 获取规则快照（如果有规则ID）
            $ruleSnapshot = null;
            if (!empty($detail['ruleId'])) {
                $ruleSnapshot = $this->getConsignmentRuleSnapshot($detail['ruleId']);
            }

            $formattedDetail = [
                'id' => $detail['id'],
                'settlementId' => $settlementOrderId,
                'settlementNo' => $settlementNo,
                'orderId' => $detail['orderId'] ?? 0,
                'orderNo' => $detail['orderNo'] ?? '',
                'outId' => $detail['outId'] ?? 0,
                'outNo' => $detail['outNo'] ?? '',
                'materielId' => $detail['materielId'] ?? 0,
                'materielName' => $detail['materielName'] ?? '',
                'materielCode' => $detail['materielCode'] ?? '',
                'skuId' => $detail['skuId'],
                'skuName' => $detail['skuName'] ?? '',
                'unitName' => $detail['unitName'] ?? '',
                'num' => $detail['num'] ?? 0, // buyNum 对应 num 字段
                'unitPrice' => $detail['unitPrice'] ?? 0,
                'totalPrice' => $detail['totalPrice'] ?? 0,
                'supplierId' => $detail['supplierId'],
                'supplierName' => $detail['supplierName'] ?? '',
                'settlementAmount' => $detail['settlementAmount'] ?? 0,
                'settlementRate' => $detail['settlementRate'] ?? 0,
                'settlementType' => $detail['settlementType'] ?? 4, // 默认固定金额类型
                'actualAmount' => $detail['actualAmount'] ?? $detail['settlementAmount'] ?? 0,
                'ruleId' => $detail['ruleId'] ?? null,
                'ruleSnapshot' => $ruleSnapshot ? json_encode($ruleSnapshot, JSON_UNESCAPED_UNICODE) : null,
                'warehouseId' => $detail['warehouseId'] ?? 0,
                'warehouseName' => $detail['warehouseName'] ?? '',
                'remark' => $detail['remark'] ?? '结算单生成时自动回写',
            ];

            $formattedList[] = $formattedDetail;
        }

        return $formattedList;
    }

    /**
     * 获取分账规则快照
     * @param int $ruleId 规则ID
     * @return array|null 规则快照数据
     */
    private function getConsignmentRuleSnapshot($ruleId)
    {
        try {
            if (empty($ruleId)) {
                return null;
            }

            $objDConsignmentRule = new \JinDouYun\Dao\Consignment\DConsignmentRule();
            $objDConsignmentRule->setTable($this->enterpriseId);

            $rule = $objDConsignmentRule->findById($ruleId);
            if (empty($rule)) {
                Logger::logs(E_USER_WARNING, '分账规则不存在', __CLASS__, __LINE__, [
                    'ruleId' => $ruleId
                ]);
                return null;
            }

            // 构建规则快照
            $ruleSnapshot = [
                'ruleId' => $rule['id'],
                'ruleName' => $rule['ruleName'] ?? '',
                'ruleType' => $rule['ruleType'] ?? 4, // 默认固定金额
                'ruleContent' => $rule['ruleContent'] ?? '',
                'priority' => $rule['priority'] ?? 0,
                'startTime' => $rule['startTime'] ?? null,
                'endTime' => $rule['endTime'] ?? null,
                'status' => $rule['status'] ?? 5,
                'createTime' => $rule['createTime'] ?? null,
                'snapshotTime' => time(), // 快照创建时间
            ];

            // 如果有SKU配置，也包含进来
            if (!empty($rule['skuConfig'])) {
                $ruleSnapshot['skuConfig'] = is_string($rule['skuConfig']) ?
                    json_decode($rule['skuConfig'], true) : $rule['skuConfig'];
            }

            return $ruleSnapshot;
        } catch (\Exception $e) {
            Logger::logs(E_USER_ERROR, '获取分账规则快照异常', __CLASS__, __LINE__, [
                'ruleId' => $ruleId,
                'exception' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * 在Model层实现批量回写结算详情数据
     * 通过UPDATE操作更新现有分账明细记录的结算相关字段
     * @param int $settlementOrderId 结算单ID
     * @param string $settlementNo 结算单号
     * @param array $detailList 结算详情数据列表
     * @return bool 成功返回true，失败返回false
     */
    private function batchWriteBackSettlementDetailsInModel($settlementOrderId, $settlementNo, array $detailList)
    {
        try {
            if (empty($settlementOrderId) || empty($settlementNo) || empty($detailList)) {
                Logger::logs(E_USER_WARNING, '批量回写参数验证失败', __CLASS__, __LINE__, [
                    'settlementOrderId' => $settlementOrderId,
                    'settlementNo' => $settlementNo,
                    'detailCount' => count($detailList)
                ]);
                return false;
            }

            // 开始事务
            $this->objDConsignmentSettlementDetail->beginTransaction();

            $successCount = 0;
            $currentTime = time();

            $ids = array_column($detailList, 'id');
            // 准备回写数据（只更新结算相关字段）
            $updateData = [
                'settlementId' => $settlementOrderId,
                'settlementNo' => $settlementNo,
                'settlementStatus' => 2, // 结算中状态
                'settlementTime' => $currentTime, // 使用时间戳格式
                'updateTime' => $currentTime
            ];

            $whereCondition = [
                'id' => $ids,
            ];
            $result = $this->objDConsignmentSettlementDetail->update($updateData, $whereCondition);
            if ($result === false) {
                Logger::logs(E_USER_ERROR, '批量回写结算详情数据失败', __CLASS__, __LINE__, [
                    'settlementOrderId' => $settlementOrderId,
                    'detailCount' => count($detailList),
                    'error' => $this->objDConsignmentSettlementDetail->error()
                ]);
                $this->objDConsignmentSettlementDetail->rollback();
                return false;
            }

            // 提交事务
            $this->objDConsignmentSettlementDetail->commit();

            Logger::logs(E_USER_NOTICE, '批量回写结算详情数据成功', __CLASS__, __LINE__, [
                'settlementOrderId' => $settlementOrderId,
                'settlementNo' => $settlementNo,
                'totalCount' => count($detailList),
                'successCount' => $successCount
            ]);

            return true;
        } catch (\Exception $e) {
            if ($this->objDConsignmentSettlementDetail->inTransaction()) {
                $this->objDConsignmentSettlementDetail->rollback();
            }
            Logger::logs(E_USER_ERROR, '批量回写结算详情数据异常', __CLASS__, __LINE__, [
                'settlementOrderId' => $settlementOrderId,
                'settlementNo' => $settlementNo,
                'exception' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 验证供应商仓储结算单数据
     * @param array $data 结算单数据
     * @return ResultWrapper 结果包装器
     */
    private function validateSupplierStorageSettlementData($data)
    {
        try {
            // 验证必要字段
            if (empty($data['supplierId'])) {
                return ResultWrapper::fail('供应商ID不能为空', ErrorCode::$paramError);
            }

            if (empty($data['settlementStartDate']) || empty($data['settlementEndDate'])) {
                return ResultWrapper::fail('结算周期不能为空', ErrorCode::$paramError);
            }

            // 验证数据类型
            if (!is_numeric($data['supplierId']) || $data['supplierId'] <= 0) {
                return ResultWrapper::fail('供应商ID必须为正整数', ErrorCode::$paramError);
            }

            // 验证日期格式
            if (!strtotime($data['settlementStartDate']) || !strtotime($data['settlementEndDate'])) {
                return ResultWrapper::fail('结算日期格式不正确', ErrorCode::$paramError);
            }

            // 验证日期范围
            if (strtotime($data['settlementStartDate']) >= strtotime($data['settlementEndDate'])) {
                return ResultWrapper::fail('开始日期必须小于结束日期', ErrorCode::$paramError);
            }

            return ResultWrapper::success('验证通过');
        } catch (\Exception $e) {
            Logger::logs(E_USER_ERROR, '验证供应商仓储结算单数据异常', __CLASS__, __LINE__, $e->getMessage());
            return ResultWrapper::fail('验证供应商仓储结算单数据失败: ' . $e->getMessage(), ErrorCode::$systemError);
        }
    }

    /**
     * 计算供应商仓储结算金额（基于固定价格）
     * @param array $data 结算单数据
     * @return ResultWrapper 结果包装器
     */
    private function calculateSupplierStorageSettlementAmount($data)
    {
        try {
            // 如果直接传入了金额，则使用传入的金额
            if (isset($data['settlementAmount']) && $data['settlementAmount'] > 0) {
                return ResultWrapper::success([
                    'totalAmount' => floatval($data['settlementAmount']),
                    'actualAmount' => floatval($data['actualAmount'] ?? $data['settlementAmount']),
                    'orderCount' => intval($data['orderCount'] ?? 0)
                ]);
            }

            // 基于分账明细计算（供应商仓储结算）
            $objMConsignmentSettlementDetail = new MConsignmentSettlementDetail($this->enterpriseId, $this->userId);

            $detailParams = [
                'supplierId' => $data['supplierId'],
                'startDate' => $data['settlementStartDate'],
                'endDate' => $data['settlementEndDate'],
                'settlementStatus' => 1, // 只查询待结算的明细
            ];

            $detailResult = $objMConsignmentSettlementDetail->getSettlementDetailList($detailParams);
            if (!$detailResult->isSuccess()) {
                return ResultWrapper::fail('获取分账明细失败: ' . $detailResult->getData(), ErrorCode::$systemError);
            }

            $detailData = $detailResult->getData();
            $detailList = $detailData['list'] ?? [];

            // 计算汇总数据（基于固定价格）
            $totalAmount = 0;
            $orderCount = 0;
            $orderIds = [];

            foreach ($detailList as $detail) {
                // 供应商仓储结算基于固定价格计算
                $settlementAmount = floatval($detail['settlementAmount']);
                $totalAmount += $settlementAmount;

                if (!in_array($detail['orderId'], $orderIds)) {
                    $orderIds[] = $detail['orderId'];
                    $orderCount++;
                }
            }

            $actualAmount = $totalAmount; // 实际金额等于结算金额

            return ResultWrapper::success([
                'totalAmount' => $totalAmount,
                'actualAmount' => $actualAmount,
                'orderCount' => $orderCount,
                'detailCount' => count($detailList),
                'detailList' => $detailList // 添加明细列表用于回写
            ]);
        } catch (\Exception $e) {
            Logger::logs(E_USER_ERROR, '计算供应商仓储结算金额异常', __CLASS__, __LINE__, $e->getMessage());
            return ResultWrapper::fail('计算供应商仓储结算金额失败: ' . $e->getMessage(), ErrorCode::$systemError);
        }
    }

    /**
     * 回写供应商仓储结算详情数据
     * @param int $settlementOrderId 结算单ID
     * @param string $settlementNo 结算单号
     * @param array $calculationData 计算数据
     * @return ResultWrapper 结果包装器
     */
    private function writeBackSupplierStorageSettlementDetails($settlementOrderId, $settlementNo, $calculationData)
    {
        try {
            Logger::logs(E_USER_NOTICE, '开始回写供应商仓储结算详情数据', __CLASS__, __LINE__, [
                'settlementOrderId' => $settlementOrderId,
                'settlementNo' => $settlementNo,
                'detailCount' => isset($calculationData['detailList']) ? count($calculationData['detailList']) : 0
            ]);

            // 获取结算明细数据
            $detailList = $calculationData['detailList'] ?? [];
            if (empty($detailList)) {
                Logger::logs(E_USER_WARNING, '没有找到需要回写的供应商仓储结算明细数据', __CLASS__, __LINE__, [
                    'settlementOrderId' => $settlementOrderId,
                    'calculationData' => $calculationData
                ]);
                return ResultWrapper::success('没有需要回写的明细数据');
            }

            // 批量更新分账明细的结算状态
            $objMConsignmentSettlementDetail = new MConsignmentSettlementDetail($this->enterpriseId, $this->userId);

            $updateCount = 0;
            foreach ($detailList as $detail) {
                $updateData = [
                    'settlementOrderId' => $settlementOrderId,
                    'settlementNo' => $settlementNo,
                    'settlementStatus' => 2, // 结算中状态
                    'updateTime' => time()
                ];

                $updateResult = $objMConsignmentSettlementDetail->updateSettlementDetailStatus($detail['id'], $updateData);
                if ($updateResult->isSuccess()) {
                    $updateCount++;
                } else {
                    Logger::logs(E_USER_WARNING, '更新供应商仓储结算明细状态失败', __CLASS__, __LINE__, [
                        'detailId' => $detail['id'],
                        'error' => $updateResult->getData()
                    ]);
                }
            }

            Logger::logs(E_USER_NOTICE, '供应商仓储结算详情数据回写完成', __CLASS__, __LINE__, [
                'settlementOrderId' => $settlementOrderId,
                'totalDetails' => count($detailList),
                'updatedDetails' => $updateCount
            ]);

            return ResultWrapper::success('供应商仓储结算详情数据回写成功');
        } catch (\Exception $e) {
            Logger::logs(E_USER_ERROR, '回写供应商仓储结算详情数据异常', __CLASS__, __LINE__, $e->getMessage());
            return ResultWrapper::fail('回写供应商仓储结算详情数据失败: ' . $e->getMessage(), ErrorCode::$systemError);
        }
    }

    /**
     * 验证时间戳格式是否正确
     * @param mixed $timestamp - 待验证的时间戳
     * @return bool - 是否为有效的时间戳
     */
    private function validateTimestamp($timestamp)
    {
        // 检查是否为数字
        if (!is_numeric($timestamp)) {
            return false;
        }

        // 转换为整数
        $timestamp = intval($timestamp);

        // 检查时间戳范围（1970年到2100年之间）
        $minTimestamp = 0; // 1970-01-01
        $maxTimestamp = 4102444800; // 2100-01-01

        return $timestamp >= $minTimestamp && $timestamp <= $maxTimestamp;
    }

}
